from langchain_community.document_loaders import PyMuPDFLoader
import os
import requests
from langchain_unstructured import UnstructuredLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from uuid import uuid4
from customtypes import Payload, SourceI
from fastapi import HTT<PERSON>Exception
from langchain_community.document_loaders import RecursiveUrlLoader, WebBaseLoader
import re
from bs4 import BeautifulSoup
from langchain_community.document_loaders import TextLoader


def bs4_extractor(html: str) -> str:
    print(html)
    soup = BeautifulSoup(html, "lxml")
    return re.sub(r"\n\n+", "\n\n", soup.text).strip()


# from langchain_community.document_loaders import UnstructuredPDFLoader


class DocumentProcesser:
    def __init__(self):
        pass

    def custom_text_splitter(self, docs, char: str):
        print(docs)
        # split docs on char
        return docs.split(char)

    # def splitter(self, docs, config: Payload):
    #     print("in the splitter")
    #     # print(docs[0].page_content)
    #     splits = None
       
    #     print("split_documents Func")
    #     text_splitter = RecursiveCharacterTextSplitter(
    #         chunk_size=config["config"]['chunk_size'], chunk_overlap=config["config"]['chunk_overlap'])
    #     splits = text_splitter.split_documents(docs)

    #     print("splits",splits)
    #     uuids = [str(uuid4()) for _ in range(len(splits))]
    #     return {"splits": splits, "ids": uuids}

    def __deleteFile(self, file_path):
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Deleted file: {file_path}")
            return True
        else:
            print(f"File not found: {file_path}")
            return False

    def __doc_loader(self, file_path):
        return UnstructuredLoader(file_path).load()

    def __pdf_loader(self, file_path):
        return PyMuPDFLoader(file_path).load()

    def __web_loader(self, url):
        return UnstructuredLoader(web_url=url).load()

    def __download(self, url) -> str | bool:
        # Ensure the blob folder exists
        blob_folder = os.path.join(os.getcwd(), 'blob')
        os.makedirs(blob_folder, exist_ok=True)

        # Extract filename from the URL
        filename = os.path.basename(url)
        file_path = os.path.join(blob_folder, filename)

        # Download the file
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()

            # Save the file to the blob folder
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print(f"Downloaded: {filename}")
            return file_path
        except requests.exceptions.RequestException as e:
            return False

    def process(self, source: SourceI, config: Payload):
        # print(source)
        if source['source_type'] == "document":
            if source['document'] is None:
                raise HTTPException(
                    status_code=400, detail="Document key required for document source")
            else:
                file_path = self.__download(source['document']['document_url'])                
                print("Downloaded: ",file_path)
                if file_path is False:
                    raise HTTPException(
                        status_code=400, detail="Failed to download file")

                # splits = None
                docs = None

                if source['document']['source_type'] == "pdf":
                    docs = self.__pdf_loader(file_path)

                elif source['document']['source_type'] in ("docx", "txt", 'pptx',"xlsx"):
                    print("its a ",source['document']['source_type'])
                    docs = self.__doc_loader(file_path)
                    # print(docs)
                else:
                    self.__deleteFile(file_path)
                    raise HTTPException(
                        status_code=400, detail="Invalid source type")

                # splits = self.splitter(docs=docs, config=config)
                self.__deleteFile(file_path)
                print("Deleting the File and returning docs")
                # print(splits)
                return docs
        elif source['source_type'] == "webpage":
            print("its a webpage")
            if source['webpage'] is None:
                raise HTTPException(
                    status_code=400, detail="Webpage key required for webpage source")

            else:
                docs = self.__web_loader(source['webpage']['webpage_url'])
                print(docs)
                # splits = self.splitter(docs=docs, config=config)
                # print(splits)
                return docs

    