from customtypes import Payload
from langchain_experimental.text_splitter import SemanticChunker
from langchain.text_splitter import (
    CharacterTextSplitter,
    RecursiveCharacterTextSplitter,
    SentenceTransformersTokenTextSplitter,
    TokenTextSplitter

)
from uuid import uuid4

class Splitter:
    config: Payload = None
    embeddings = None
    docs = None
    def __init__(self, config: Payload, docs, embeddings):
        self.config = config
        self.docs = docs
        self.embeddings = embeddings



    def getUUIDs(self, number_of_splits):
        return [str(uuid4()) for _ in range(number_of_splits or 0)]


    def split(self):
        print("in the splitter for (", self.config["config"]['chunking_strategy'], ") to run !!!")
        match self.config["config"]['chunking_strategy']:
            case "CharacterTextSplitter":
               return self.characterTextSplitter()
            case "RecursiveCharacterTextSplitter":
                return self.recursiveCharacterTextSplitter()
            case "SentenceTransformersTokenTextSplitter":
                return self.sentenceTransformersTokenTextSplitter()
            case "TokenTextSplitter":
                return self.tokenTextSplitter() 
            case "semantic": # percentile standard_deviation interquartile gradient
                return self.semanticTextSplitter()
            case _:
                raise Exception("chunking strategy not supported")

    def characterTextSplitter(self):
        char_splitter = CharacterTextSplitter(chunk_size=self.config["config"]['chunk_size'], chunk_overlap=self.config["config"]['chunk_overlap'],separator=self.config["config"]['separator'])
        char_docs = char_splitter.split_documents(self.docs)
        return {"splits": char_docs, "ids": self.getUUIDs(len(char_docs))}
 
    
    def recursiveCharacterTextSplitter(self):
        char_splitter = RecursiveCharacterTextSplitter(chunk_size=self.config["config"]['chunk_size'], chunk_overlap=self.config["config"]['chunk_overlap'], separators=self.config["config"]['separators'],keep_separator=self.config["config"]['keep_separator'])
        char_docs = char_splitter.split_documents(self.docs)

        return {"splits": char_docs, "ids": self.getUUIDs(len(char_docs))}
    
    def sentenceTransformersTokenTextSplitter(self):    
        sent_splitter = SentenceTransformersTokenTextSplitter(chunk_size=self.config["config"]['chunk_size'],chunk_overlap=self.config["config"]['chunk_overlap'],model_name=self.config["config"]['model_name'],tokens_per_chunk=self.config["config"]['tokens_per_chunk'])
        sent_docs = sent_splitter.split_documents(self.docs)
        return {"splits": sent_docs, "ids": self.getUUIDs(len(sent_docs))}
    
    def tokenTextSplitter(self):
        token_splitter = TokenTextSplitter(chunk_overlap=0, chunk_size=512)
        token_docs = token_splitter.split_documents(self.docs)
        return {"splits": token_docs, "ids": self.getUUIDs(len(token_docs))}
    
    def semanticTextSplitter(self):
        doc_text = ""
        for doc in self.docs:
            doc_text += doc.page_content + "\n"
            
        text_splitter = SemanticChunker(self.embeddings,breakpoint_threshold_type=self.config["config"]["breakpoint_threshold_type"])
        semantic_docs = text_splitter.create_documents([doc_text])
        return {"splits": semantic_docs, "ids": self.getUUIDs(len(semantic_docs))}
