from llm import OpenAI
from services import DocumentProcesser
from store import ChromaStore
from fastapi import FastAPI
from factories import Rag
from pydantic import BaseModel, Field
from dataclasses import dataclass
from typing import Dict, Optional, Literal
from fastapi.middleware.cors import CORSMiddleware

from customtypes import Payload, LlmConfig, StoreConfig, PayloadConfig, SourceI, PayloadRefine, PayloadAgent
import os
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "pr-mundane-morsel-56"
os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"

app = FastAPI(root_path="/api/v1")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
    allow_credentials=True,
)


@app.get("/")
def read_root():
    return {"Hello": "World"}


@dataclass
class TestLLMConfig:
    llm_key: str
    llm_type: str


class TestLLMBodyModel(BaseModel):
    llm_config: TestLLMConfig


@app.post("/test/llm")
def testLLM(body: TestLLMBodyModel):
    return {"isGood": Rag(body.model_dump()).testLLM()}


@dataclass
class TestStoreConfig:
    store_url: str
    store_type: str


class TestStoreBodyModel(BaseModel):
    store_config: TestStoreConfig


@app.post("/test/store")
def testStore(body: TestStoreBodyModel):
    return {"isGood": Rag(body.model_dump()).testStore()}


class UploadBodyModel(BaseModel):
    llm_config: LlmConfig
    store_config: StoreConfig
    config: PayloadConfig
    source: SourceI


@app.post("/upload")
def upload(body: UploadBodyModel):
    print(body.model_dump())
    return Rag(body.model_dump()).uploadFileV2(body.model_dump()['source'])


class GetFilesBodyModel(BaseModel):
    llm_config: LlmConfig
    store_config: StoreConfig
    config: PayloadConfig


@app.post("/getFiles")
def getFilesfn(body: GetFilesBodyModel):
    print("/getFiles")
    print(body.model_dump())
    return Rag(body.model_dump()).getFiles()
    # return {"isGood": Rag().testLLM(.get)}


class GetDocumentBodyModel(BaseModel):
    llm_config: LlmConfig
    store_config: StoreConfig
    config: PayloadConfig
    file_name: str


@app.post("/getStoreData")
def getFIleDoc(body: GetDocumentBodyModel):
    print(body.model_dump())
    return Rag(body.model_dump()).getFileDocuments(body.file_name)
    # return {"isGood": Rag().testLLM(.get)}


@app.post("/deleteFile")
def delete(body: GetDocumentBodyModel):
    return Rag(body.model_dump()).deleteFile(body.file_name)


@app.post("/resetStore")
def delete(body: GetFilesBodyModel):
    return Rag(body.model_dump()).resetStore()


class HistoryQuery(BaseModel):
    question: str
    answer: Optional[str] = None


class QueryBodyModel(BaseModel):
    llm_config: LlmConfig
    store_config: StoreConfig
    config: PayloadConfig
    query_history: list[HistoryQuery]

# class AgentBodyModel(BaseModel):
#     llm_config: LlmConfig
#     task: str
#     data: dict = {}
#     custom_prompt: str = None
#     output_structure: dict = {}


class AgentBodyModel(BaseModel):
    llm_config: LlmConfig
    payload: PayloadAgent


@app.post("/query/v1")
def query(body: QueryBodyModel):
    print(body.model_dump())
    return Rag(body.model_dump()).genericQuery(body.query_history)


@app.post("/getFile")
def delete(body: GetDocumentBodyModel):
    return Rag(body.model_dump()).getFile(body.file_name)


class RefineBodyModel(BaseModel):
    llm_config: LlmConfig
    store_config: StoreConfig
    config: PayloadConfig
    query_history: list[HistoryQuery]
    refine: list[dict]


# @app.post("/refine")
# def query(body: RefineBodyModel):
#     # print("--------")
#     # print(body.model_dump())
#     return Rag(body.model_dump()).refineV2(refinePayload=body.refine, user_query=body.user_question)

@app.post("/refinev2")
def query(body: RefineBodyModel):
    print("--------")
    print(body.model_dump()['config'])
    print("--------")
    return Rag(body.model_dump()).refineV2(refinePayload=body.refine, query_history=body.query_history)


@app.post("/agent")
def query(body: AgentBodyModel):
    print("--------")
    print(body.model_dump())
    print("--------")
    return Rag(body.model_dump()).agentBlock(payload=body.payload)


@app.post("/test/splitters")
def upload(body: UploadBodyModel):
    print(body.model_dump())
    return Rag(body.model_dump()).testSplits(body.model_dump()['source'])

# tst_pdf_url = "https://storage.botforce.ai/Bots/duaa-669/unstructuredResources/BBhpzEHQ8ufZ0sPboUnu6gx57AsTs0YabRzO5b7jSKmNi5RGHwYAWOL1XphS0V.pdf"
# tst_txt_url = "https://storage.botforce.ai/Bots/duaa-669/unstructuredResources/cWVuSrRso6ToAmrqaPVxQ7KDep09ZR4yyTvEFksvOo8vvqUdSyhpCTP6dkAgCF.txt"
# the_ultimate_config={
#     "llm_config": {"llm_key":"******************************************************","llm_type":"openai","llm_model":"gpt-4o-mini","llm_temperature":0},
#     "store_config": {"store_url":"http://192.168.2.13:8000","collection_name":"mamaCry","store_type":"chroma"},
#     "config": {"chunk_overlap":50,"chunk_size":300},
#     }
