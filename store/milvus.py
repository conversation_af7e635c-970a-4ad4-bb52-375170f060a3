from customtypes import Payload
from .abstract import Store
import chromadb
from langchain_chroma import Chroma
from langchain_milvus import Mi<PERSON><PERSON><PERSON>
from pymilvus import MilvusClient


from langchain_community.vectorstores.utils import filter_complex_metadata
class MilvusStore(Store):

    config: Payload = None
    def __init__(self, config:Payload):
        self.config = config


    def connect(self):
        pass
        

    def test(self):
        try:
            MilvusClient(uri=self.config["store_config"]['store_url'])
            return True
        except Exception as e:
            print(e)
            return False

 
        
    def upload(self, splited_docs, embedding):
        try:
            collection_name = self.config["store_config"]['collection_name']
            Milvus.from_documents(
                documents=filter_complex_metadata(splited_docs['splits']),
                embedding=embedding,
                collection_name=collection_name,
                connection_args={"uri": self.config["store_config"]['store_url']},
            )

            return True
        except Exception as e:
            print(e)
            return False
    
    def get(self,files_to_delete):
        pass


    def delete(self,file_name):
        print(file_name)
        try:
            collection_name = self.config["store_config"]['collection_name']
            client = MilvusClient(
                uri=self.config["store_config"]['store_url']
            )
            res = client.delete(
            collection_name=collection_name,
            filter=f"filename == '{file_name}'"
            )
            print(res)
            if res['delete_count'] > 0:
                return True
            else:
                return False
            
        except Exception as e:
            return False


    def reset(self):
            try:
                collection_name = self.config["store_config"]['collection_name']
                milvus = MilvusClient(
                    uri=self.config["store_config"]['store_url']
                )
                if milvus.has_collection(collection_name):
                    milvus.drop_collection(collection_name)
                    return True
                
                return False
            except Exception as e:
                print(e)
                return False
                


    def getFiles(self):
        collection_name = self.config["store_config"]['collection_name']
        milvus = MilvusClient(
                uri=self.config["store_config"]['store_url']
            )
        res = milvus.query(
                collection_name=collection_name,
                filter="pk >= 0", 
                output_fields = ["filename","filetype"]

                )
        files = []
        for metadata in res:
            object = {
                "filename": metadata['filename'],
                "filetype": metadata['filetype']
            }
            if object not in files:
                files.append(object)
        return files
    
    def getFile(self,file_name: str):
        try:
            collection_name = self.config["store_config"]['collection_name']
            milvus = MilvusClient(
                uri=self.config["store_config"]['store_url']
            )
            res = milvus.query(
                collection_name=collection_name,
                filter="filename == '" + file_name + "'", 
                output_fields = ["text"]

                )
            print(res)
            final_res = []
            for x in res:
                final_res.append(x["text"])
                
            return final_res
        except Exception as e:
            print(e)
            return []

    def getFileDocuments(self, file_name: str):
        try:
            collection_name = self.config["store_config"]['collection_name']
            milvus = MilvusClient(
                uri=self.config["store_config"]['store_url']
            )
            res = milvus.query(
                collection_name=collection_name,
                filter="pk >= 0", 
                output_fields = ["text"]

                )
            print(res)
            final_res = []
            for x in res:
                final_res.append(x["text"])

            return final_res
        except Exception as e:
            print(e)
            return []
    
    def getVectorStore(self,embedding = None):
       collection_name = self.config["store_config"]['collection_name']
    #    persistent_client = MilvusClient(uri=self.config["store_config"]['store_url'])
       if embedding is not None:
            return Milvus(
                connection_args={"uri": self.config["store_config"]['store_url']},
                collection_name=collection_name,
                embedding_function=embedding
            )
       else:
            return Milvus(
                connection_args={"uri": self.config["store_config"]['store_url']},
                collection_name=collection_name,
                create_collection_if_not_exists=False
                )

       


    


