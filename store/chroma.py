from customtypes import Payload
from .abstract import Store
import chromadb
from langchain_chroma import Chroma
from langchain_community.vectorstores.utils import filter_complex_metadata
class ChromaStore(Store):

    config: Payload = None
    def __init__(self, config:Payload):
        self.config = config


    def connect(self):
        pass
        

    def test(self):
        try:
            chromadb.HttpClient(host=self.config["store_config"]['store_url'])
            return True
        except Exception as e:
            print(e)
            return False

    # def upload(self, splited_docs, embedding):
    #     try:
    #         collection_name = self.config["store_config"]['collection_name']
    #         persistent_client = chromadb.HttpClient(host=self.config["store_config"]['store_url'])
    #         col = persistent_client.get_or_create_collection(collection_name)
    #         # col.add(documents=splited_docs['splits'], ids=splited_docs['ids'])

    #         vectorstore = Chroma(
    #             client=persistent_client,
    #             collection_name=collection_name,
    #             embedding_function=embedding
    #         )
    #         vectorstore.add_documents(documents=filter_complex_metadata(splited_docs['splits']), ids=splited_docs['ids'])
    #         return True
    #     except Exception as e:
    #         print(e)
    #         return False
        
    def upload(self, splited_docs, embedding):
        try:
            collection_name = self.config["store_config"]['collection_name']
            persistent_client = chromadb.HttpClient(host=self.config["store_config"]['store_url'])
            print("connected", persistent_client)
            persistent_client.get_or_create_collection(collection_name)


            # col.add(documents=splited_docs['splits'], ids=splited_docs['ids'])

            vectorstore = Chroma.from_documents(
                client=persistent_client,
                collection_name=collection_name,
                embedding=embedding,
                # collection_metadata={'azzam': "mama yo"},
                documents=filter_complex_metadata(splited_docs['splits'])
            )
            
            # vectorstore.add_documents(documents=filter_complex_metadata(splited_docs['splits']), ids=splited_docs['ids'])
            return True
        except Exception as e:
            print(e)
            return False
    
    def get(self,files_to_delete):
        pass


    def delete(self,file_name):
        # TODO: To optimise
        try:
            collection_name = self.config["store_config"]['collection_name']
            persistent_client = chromadb.HttpClient(host=self.config["store_config"]['store_url'])
            db = Chroma(
                client=persistent_client,
                collection_name=collection_name,
            )
            # db.reset_collection()
            coll = db.get()
            ids_to_del = []

            for idx in range(len(coll['ids'])):

                id = coll['ids'][idx]
                metadata = coll['metadatas'][idx]

                if metadata['filename'] or metadata['title'] in file_name:
                    ids_to_del.append(id)

            db._collection.delete(ids_to_del)
            return True
        except Exception as e:
            return False


    def reset(self):
            # TODO: To optimise
            try:
                collection_name = self.config["store_config"]['collection_name']
                persistent_client = chromadb.HttpClient(host=self.config["store_config"]['store_url'])
                db = Chroma(
                    client=persistent_client,
                    collection_name=collection_name,
                )
                db.reset_collection()
                return True
            except Exception as e:
                print(e)
                return False
                


    def getFiles(self):
        collection_name = self.config["store_config"]['collection_name']
        persistent_client = chromadb.HttpClient(host=self.config["store_config"]['store_url'])
        # ss = persistent_client.get_collection(collection_name).get().get("ids")
        # print("---------------------------")
        # print(ss)
        # print("---------------------------")
        # return []
        db = Chroma(
            client=persistent_client,
            collection_name=collection_name,
            create_collection_if_not_exists=False
        )
        # return db.get(include=['metadatas'])
        # print(db)
        meta_datas = db.get(include=['metadatas'])
        # return meta_datas
        print("---------------------------")
        print(meta_datas)
        print("---------------------------")
        # return
        files = []
        for metadata in meta_datas.get("metadatas"):
            if metadata:
                object = {
                    "filename": metadata['filename'] or metadata['title'],
                    "filetype": metadata['filetype'] or metadata['format'] 
                }
                if object not in files:
                    files.append(object)
        return files

    def getFileDocuments(self, file_name: str):
        db = self.getVectorStore()
        return db.get().get('documents')
    

    def getFile(self, file_name: str):
        db = self.getVectorStore()
        # file = db.get(where={"$or": [{"filename": file_name}, {"title": file_name}]}).get('documents')
        file = db.get().get('documents')

        print(file_name)
        return file

    
    def getVectorStore(self,embedding = None):
       collection_name = self.config["store_config"]['collection_name']
       persistent_client = chromadb.HttpClient(host=self.config["store_config"]['store_url'])
       if embedding is not None:
            return Chroma(
                client=persistent_client,
                collection_name=collection_name,
                embedding_function=embedding
            )
       else:
            return Chroma(
                    client=persistent_client,
                    collection_name=collection_name,
                    create_collection_if_not_exists=False
                )

       


    


