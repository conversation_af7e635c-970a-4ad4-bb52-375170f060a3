from abc import ABC, abstractmethod
from langchain_chroma import Chroma

class Store(ABC):
    @abstractmethod
    def connect(self):
        pass
    
    @abstractmethod
    def test(self) -> bool:
        pass

    @abstractmethod
    def upload(self, doc):
        pass
    
    @abstractmethod
    def delete(self, key):
        pass

    @abstractmethod
    def reset(self):
        pass


    @abstractmethod
    def get(self):
        pass

    @abstractmethod
    def getFiles(self):
        pass

    @abstractmethod
    def getFileDocuments(self, file_name:str):
        pass

    @abstractmethod
    def getFile(self, file_name:str):
        pass

    @abstractmethod
    def getVectorStore(self, embedding = None) -> Chroma:
        pass
    