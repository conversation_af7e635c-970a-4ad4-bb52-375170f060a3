from llm.abstract import LlmAbstract
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from customtypes import Payload
import requests
import json


class OpenAI(LlmAbstract):

    config: Payload = None

    def __init__(self, config:Payload):
        self.config = config

    def test(self):
        try:
           response = requests.get("https://api.openai.com/v1/models", headers={"Content-Type": "application/json", "Authorization": f"Bearer {self.config['llm_config']['llm_key']}"})
           print(response)
           if response.status_code == 200:
               return True
           else:
               return False
        except Exception as e:
            print(e)
            return False




    def generateEmbedding(self) -> OpenAIEmbeddings:
        return OpenAIEmbeddings(model="text-embedding-3-small",openai_api_key=self.config["llm_config"]["llm_key"])
    
    def getModel(self):
        return ChatOpenAI(model=self.config["llm_config"]["llm_model"],openai_api_key=self.config["llm_config"]["llm_key"],temperature=self.config["llm_config"]["llm_temperature"]) 


