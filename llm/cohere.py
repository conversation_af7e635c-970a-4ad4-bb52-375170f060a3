from llm.abstract import L<PERSON><PERSON>bstract
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from customtypes import Payload
import requests
import json
from langchain_cohere import CohereEmbeddings, ChatCohere


class Cohere(LlmAbstract):

    config: Payload = None

    def __init__(self, config:Payload):
        self.config = config

    def test(self):
        try:
           response = requests.post("https://api.cohere.com/v1/check-api-key", headers={"Authorization": f"Bearer {self.config['llm_config']['llm_key']}"})
           if response.json()["valid"]:
               return True
           else:
               return False
        except Exception as e:
            print(e)
            return False




    def generateEmbedding(self) -> OpenAIEmbeddings:
        return CohereEmbeddings(model="embed-multilingual-v3.0",cohere_api_key=self.config["llm_config"]["llm_key"])


        # return OpenAIEmbeddings(model="text-embedding-3-small",openai_api_key=self.config["llm_config"]["llm_key"])
    
    def getModel(self):
        return ChatCohere(model=self.config["llm_config"]["llm_model"],cohere_api_key=self.config["llm_config"]["llm_key"],temperature=self.config["llm_config"]["llm_temperature"]) 


