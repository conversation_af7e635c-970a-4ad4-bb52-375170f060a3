from customtypes import Payload, SourceI, PayloadRefine, PayloadAgent
from llm import OpenAI, LlmAbstract, Cohere
from store import ChromaStore, Store, MilvusStore
from services import DocumentProcesser, Splitter
import uvicorn
from fastapi import HTTPException
import json
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from langchain_core.prompts import MessagesPlaceholder
from langchain.chains.llm import LL<PERSON>hain
from langchain.schema import AIMessage, HumanMessage, SystemMessage
from langchain.schema.output_parser import StrOutputParser
from langchain_core.runnables import Runnable, RunnablePassthrough
from pydantic import BaseModel, Field, create_model
import tiktoken
from langchain_core.messages import trim_messages
from langchain.chains.history_aware_retriever import create_history_aware_retriever
from typing import List, Optional, Dict, Any
import re
from langchain_core.output_parsers import JsonOutputParser


class ResponseSchema(BaseModel):
    # page_content: str = Field(..., alias="pageContent")  # Valid name with underscore
    source_id: str
    type: str
    answer: str

# class FinalResponseSchema(BaseModel):
#     # page_content: str = Field(..., alias="pageContent")  # Valid name with underscore
#     items: list[ResponseSchema]
#     isPartial: bool
#     narrative: str


class FinalResponseSchema(BaseModel):
    items: List[ResponseSchema] = Field(
        default_list=[], description="List of relevant metadata items")
    # narrative: Optional[str] = Field(description="The answer narrative")
    isPartial: bool = Field(
        default=False, description="Whether the answer is partial")


def str_token_counter(text: str) -> int:
    enc = tiktoken.get_encoding("o200k_base")
    return len(enc.encode(text))


def tiktoken_counter(messages) -> int:
    """Approximately reproduce https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb

    For simplicity only supports str Message.contents.
    """
    num_tokens = 3  # every reply is primed with <|start|>assistant<|message|>
    tokens_per_message = 3
    tokens_per_name = 1
    for msg in messages:
        if isinstance(msg, HumanMessage):
            role = "user"
        elif isinstance(msg, AIMessage):
            role = "assistant"
        elif isinstance(msg, SystemMessage):
            role = "system"
        else:
            raise ValueError(f"Unsupported messages type {msg.__class__}")
        num_tokens += (
            tokens_per_message
            + str_token_counter(role)
            + str_token_counter(msg.content)
        )
        if msg.name:
            num_tokens += tokens_per_name + str_token_counter(msg.name)
    return num_tokens


class Rag:

    LLM: LlmAbstract = None
    STORE: Store = None
    config: Payload = None
    isArabic = False

    def __init__(self, config: Payload = None):
        self.config = config

        if "llm_config" in config:
            self.LLM = self.getLLM(config)
        if "store_config" in config:
            self.STORE = self.getStore(config)

    def getLLM(self, config: Payload):
        match config["llm_config"]["llm_type"]:
            case "openai":
                return OpenAI(config=config)
            case "cohere":
                return Cohere(config=config)
            case _:
                raise HTTPException(
                    status_code=400, detail="Llm type not supported")

    def getStore(self, config: Payload):
        match config["store_config"]["store_type"]:
            case "chroma":
                return ChromaStore(config=config)
            case "milvus":
                return MilvusStore(config=config)
            case _:
                raise Exception("Store type not supported")

    def testAll(self):
        return self.LLM.test() and self.STORE.test()

    def testLLM(self) -> bool:
        return self.LLM.test()

    def testStore(self):
        return self.STORE.test()

    def uploadFile(self, source: SourceI):
        try:
            if not self.testAll():
                print("not all test")
                return {"error": "Something went wrong, test connections failed"}
            service = DocumentProcesser()
            splits = service.process(source, self.config)
            print("i got splits", splits)
            if splits is None:
                raise HTTPException(
                    status_code=400, detail="Failed to process file and produce splits")
            embeddings = self.LLM.generateEmbedding()
            self.STORE.upload(splits, embeddings)
            raise HTTPException(
                status_code=200, detail="File uploaded successfully")
        except Exception as e:
            print(e)
            return e

    def uploadFileV2(self, source: SourceI):
        try:
            if self.config["config"]['chunking_strategy'] is None:
                raise HTTPException(
                    status_code=400, detail="chunking_strategy is a must")
            embeddings = self.LLM.generateEmbedding()
            service = DocumentProcesser()
            docs = service.process(source, self.config)

            splits = Splitter(config=self.config, docs=docs,
                              embeddings=embeddings).split()
            if splits is None:
                raise HTTPException(
                    status_code=400, detail="Failed to process file and produce splits")

            self.STORE.upload(splits, embeddings)

            raise HTTPException(
                status_code=200, detail="File uploaded successfully")
        except Exception as e:
            print(e)
            return e

    def deleteFile(self, file_name: str):
        try:
            return self.STORE.delete(file_name=file_name)
        except Exception as e:
            return {"error": "Failed to delete file: " + str(e)}

    def getFile(self, file_name: str):
        try:
            return self.STORE.getFile(file_name=file_name)
        except Exception as e:
            return {"error": "Failed to delete file: " + str(e)}

    def resetStore(self):
        try:
            return self.STORE.reset()
        except Exception as e:
            return {"error": "Failed to delete file: " + str(e)}

    def getFiles(self):
        try:
            return self.STORE.getFiles()
        except Exception as e:
            return {"error": "Failed to get files: " + str(e)}

    def getFileDocuments(self, file_name):
        try:
            return self.STORE.getFileDocuments(file_name)
        except Exception as e:
            return {"error": "Failed to get files: " + str(e)}

    def query(self, user_query: str):
        print(user_query)
        try:
            embeddings = self.LLM.generateEmbedding()
            vectorStore = self.STORE.getVectorStore(embeddings)
            retriever = vectorStore.as_retriever()
            system_prompt = (
                "You are an assistant for a transportation company called JETT your name is Ra7al question-answering tasks. "
                "you help people with questions about JETT products and services. "
                "Use the following pieces of retrieved context to answer "
                "the question. If you don't know the answer, say that you "
                "don't know. Use three sentences maximum and keep the "
                "answer concise."
                "\n\n"
                "<context> "
                "{context}"
                "</context> "
                "Just answer from context, do not conclude anything."
            )
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    ("human", "{input}"),
                ]
            )
            llm = self.LLM.getModel()

            question_answer_chain = create_stuff_documents_chain(llm, prompt)
            rag_chain = create_retrieval_chain(
                retriever, question_answer_chain)

            results = rag_chain.invoke({"input": user_query})

            return results.get("answer")
        except Exception as e:
            print(e)
            return e

    def genericQuery(self, query_history: list, additional_context: str = ""):
        if len(query_history) == 0:
            raise Exception("Query history is empty")
        if len(query_history) == 1:
            return self.queryv1(query_history[0].question, additional_context)
        else:
            print(query_history)
            return self.queryHistory(query_history, additional_context)

    def queryv1(self, user_query: str, additional_context: str = ""):
        print(user_query)
        try:
            embeddings = self.LLM.generateEmbedding()
            vectorStore = self.STORE.getVectorStore(embeddings)

            # r = vectorStore.as_retriever()

            # s = vectorStore.as_retriever(
            #     search_type="similarity",
            #     search_kwargs={"k": 20}
            # )

            mmr = vectorStore.as_retriever(
                search_type="mmr",
                search_kwargs={"k": self.config["config"]["top_k"], "fetch_k": self.config["config"]
                               ["fetch_k"], "lambda_mult": self.config["config"]["lambda_mult"]}

            )

            # sst = vectorStore.as_retriever(
            #     search_type="similarity_score_threshold",
            #     search_kwargs={"k": 4, "score_threshold": 0.1}

            # )

            if self.config["config"]["prompt"]:
                prompt = self.config["config"]["prompt"]
            else:
                prompt = ("You are an assistant for a question-answering tasks. "
                          "Use the following pieces of retrieved context to answer "
                          "the question. If you don't know the answer, say that you "
                          "don't know. Use three sentences maximum and keep the "
                          "answer concise.")

            system_prompt = (
                prompt
                +
                "\n\n"
                "<context> "
                "{context}"
                "</context> "
                "Just answer from context, do not conclude anything. "
            )
            if additional_context:
                system_prompt += "\n\nadditional context:" + \
                    additional_context.replace('{', '{{').replace('}', '}}')
            # print("prompt created", system_prompt)
            # "if the response in arabic add full diacritics to every letter. "
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    ("human", "{input}"),
                ]
            )
            # print("prompt 2", prompt)
            llm = self.LLM.getModel()

            question_answer_chain = create_stuff_documents_chain(llm, prompt)
            # rag_chainr = create_retrieval_chain(r, question_answer_chain)
            # rag_chains = create_retrieval_chain(s, question_answer_chain)
            rag_chainmmr = create_retrieval_chain(mmr, question_answer_chain)
            # rag_chainsst = create_retrieval_chain(sst, question_answer_chain)
            # print(rag_chainmmr)
            # rr = rag_chainr.invoke({"input": user_query})
            # sr = rag_chains.invoke({"input": user_query})
            mmrr = rag_chainmmr.invoke({"input": user_query})
            # print("mmrr",mmrr)
            # sttr = rag_chainsst.invoke({"input": user_query})
            return {"text":  mmrr.get("answer"), "sources": mmrr.get("context")}
            # return {
            #     # "R": {"out": "too bad cuz only 4K"},
            #     # "S": {"found": [r.page_content for r in s.invoke(user_query)], "a": sr.get("answer")},
            #     # "MMR":{"found": [r.page_content for r in mmr.invoke(user_query)], "a": mmrr.get("answer")},
            #     # "SST": {"out": "too dumb cuz only get back the docs with a simi score even with 0.1 we get bad result" }
            # }
            # return results.get("answer")
        except Exception as e:
            print(e)
            return e

    def queryHistoryRefine(self, query_history: list, faq_results, dialog_results):
        try:
            user_query = query_history[-1].question
            query_history = query_history[:-1]
            embeddings = self.LLM.generateEmbedding()
            vectorStore = self.STORE.getVectorStore(embeddings)
            # print(vectorStore,"vs")
            # NEEDS TO BE FIXED
            if self.config['store_config']['store_type'] == "milvus":
                retriever = vectorStore.as_retriever(
                    # search_type="mmr",
                    # search_kwargs={"k": self.config["config"]["top_k"],         "fetch_k": self.config["config"]
                    #             ["fetch_k"], "lambda_mult": self.config["config"]["lambda_mult"],


                    #             }
                )
            else:
                print("--------------------------")
                print(self.config["config"])
                print("--------------------------")
                retriever = vectorStore.as_retriever(
                    search_type="mmr",
                    search_kwargs={"k": self.config["config"]["top_k"], "fetch_k": self.config["config"]
                                   ["fetch_k"], "lambda_mult": self.config["config"]["lambda_mult"],


                                   }
                )

            faq_context = json.dumps(
                ([item.dict() for item in faq_results]), ensure_ascii=False)
            dialog_context = json.dumps(
                ([item.dict() for item in dialog_results]), ensure_ascii=False)
            print("---------user_query-----------------")
            print(user_query)
            print("----------user_query----------------")
            contextualize_q_system_prompt = (

                "Given a chat history, faqs, and the latest user question "
                "which might reference context in the chat history, "
                "formulate a standalone question which can be understood "
                "without the chat history. Do NOT answer the question, "
                f"reformulate the question and return it in {'arabic. ' if self.isArabic else 'english. '}"
                "\n"
                "just reformulate it if needed and otherwise return it as is.\n"
                # "its really important to remove any part from the question that can be answerd by an item in the additional context that has a type of dialog."
                "\n <faqs> " + \
                faq_context.replace('{', '{{').replace('}', '}}') + " </faqs>"
            )
            print("reformulating... ")
            # if additional_context:
            #     contextualize_q_system_prompt += "\n\n<additionalContext> " + additional_context.replace('{', '{{').replace('}', '}}')+ " </additionalContext>" + "\n use the additional context with the chat history to better formulate the question."
            contextualize_q_prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", contextualize_q_system_prompt),
                    MessagesPlaceholder("chat_history"),
                    ("human", "{input}"),
                ]
            )
            llm = self.LLM.getModel()

            history_aware_retriever = create_history_aware_retriever(
                llm, retriever, contextualize_q_prompt
            )

            if self.config["config"]["prompt"]:
                prompt = self.config["config"]["prompt"]
            else:

                prompt = (
                    "You are an assistant for a question-answering tasks.\n"

                    f"Response Guidelines:\n"
                    "\t1. Provide an answer strictly based on the \"faqs\" and \"context\".\n"
                    "\t2. Don't not infer , guess , or assume any information that is not explicity stated in the \"faqs\" and \"context\".\n"
                    "\t3. Refine from making conclusions that are not grounded in the provided \"faqs\" and \"context\".\n"
                    "\t4. If the \"faqs\" and \"context\" do not provide enough information to answer the question, don't invent missing details.\n"
                    "\t5. If you don't know the answer, return an empty string.\n"
                    "\t6. Use three sentences maximum and keep the answer concise.\n"
                    f"Important RULE: answer the question {'in arabic and add full diacritics to every letter. '  if self.isArabic else 'in english only. '}" + "\n"
                    f"{ 'use the following persona: as a source of information to help answer the user question if needed: <persona> ' + self.config['config']['persona'] + ' </persona>' if self.config['config']['persona'] else ''}"

                    "\n\n<faqs> " +
                    faq_context.replace(
                        '{', '{{').replace('}', '}}') + " </faqs>"
                )

            system_prompt = (
                prompt +
                "\n\n"
                "<context> {context} </context>"
            )
            # if additional_context:
            #     system_prompt += "\n\n additional context:" + additional_context.replace('{', '{{').replace('}', '}}')
            qa_prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    MessagesPlaceholder("chat_history"),
                    ("human", "{input}"),
                ]
            )
            question_answer_chain = create_stuff_documents_chain(
                llm, qa_prompt)
            rag_chain = create_retrieval_chain(
                history_aware_retriever, question_answer_chain)

            max_tokens = 1000

            # rewrite the history in a way that it can be used by the LLM : human and ai messages
            # print(query_history)
            history = []
            for query in query_history:
                history.append(HumanMessage(content=query.question))
                history.append(AIMessage(content=query.answer))

            history_trimmed = trim_messages(
                messages=history,
                max_tokens=max_tokens,
                strategy="last",
                token_counter=tiktoken_counter,
                include_system=True
            )

            results = rag_chain.invoke({
                "chat_history": history_trimmed,
                "input": user_query
            })
            # print("results: ",results)
            return {"text": results.get("answer"), "sources": results.get("context")}
        except Exception as e:
            print(e)
            return e

    def queryHistory(self, query_history: list, additional_context: str = ""):
        try:
            user_query = query_history[-1].question
            query_history = query_history[:-1]
            embeddings = self.LLM.generateEmbedding()
            vectorStore = self.STORE.getVectorStore(embeddings)
            retriever = vectorStore.as_retriever(
                search_type="mmr",
                search_kwargs={"k": self.config["config"]["top_k"], "fetch_k": self.config["config"]
                               ["fetch_k"], "lambda_mult": self.config["config"]["lambda_mult"]}
            )
            contextualize_q_system_prompt = (
                "Given a chat history and the latest user question "
                "which might reference context in the chat history, "
                "formulate a standalone question which can be understood "
                "without the chat history. Do NOT answer the question, "
                "just reformulate it if needed and otherwise return it as is."
            )
            contextualize_q_prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", contextualize_q_system_prompt),
                    MessagesPlaceholder("chat_history"),
                    ("human", "{input}"),
                ]
            )
            llm = self.LLM.getModel()

            history_aware_retriever = create_history_aware_retriever(
                llm, retriever, contextualize_q_prompt
            )

            if self.config["config"]["prompt"]:
                prompt = self.config["config"]["prompt"]
            else:
                prompt = ("You are an assistant for a question-answering tasks. "
                          "Use the following pieces of retrieved context to answer "
                          "the question. If you don't know the answer, say that you "
                          "don't know. Use three sentences maximum and keep the "
                          "answer concise.")

            system_prompt = (
                prompt +
                "\n\n"
                "{context}"
            )
            qa_prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    MessagesPlaceholder("chat_history"),
                    ("human", "{input}"),
                ]
            )
            question_answer_chain = create_stuff_documents_chain(
                llm, qa_prompt)
            rag_chain = create_retrieval_chain(
                history_aware_retriever, question_answer_chain)

            max_tokens = 1000

            # rewrite the history in a way that it can be used by the LLM : human and ai messages
            # print(query_history)
            history = []
            for query in query_history:
                history.append(HumanMessage(content=query.question))
                history.append(AIMessage(content=query.answer))

            history_trimmed = trim_messages(
                messages=history,
                max_tokens=max_tokens,
                strategy="last",
                token_counter=tiktoken_counter,
                include_system=True
            )

            results = rag_chain.invoke({
                "chat_history": history_trimmed,
                "input": user_query
            })

            return {"text": results.get("answer"), "sources": results.get("context")}
        except Exception as e:
            print(e)
            return e

    def refine(self, refinePayload, user_query: str):

        context = json.dumps(refinePayload, ensure_ascii=False)
        system = (
            "Given the context in the prompt and the user question, that is a FAQs and dialogs in JSON format, "
            "i want you to see what faq or dialog is relevant to the user question and "
            "return the metadata for the relevant items in JSON array. "
            "Its prefeered to return a single item, but you can return multiple items if you think the user question is relevant to multiple items. "
            # "if there was an insult in the user question, return in the array an object that have a key called 'insult' with the value of the insult. and dont return any relevant data even if there was.  "
            "If no relevant item is found, return an empty array only and not anything else. "
            "\n\n"
            f"<context> {context.replace('{', '{{').replace('}', '}}')} </context>"
        )

        # Get the model and set structured output
        model = self.LLM.getModel()
        structured_llm = model.with_structured_output(list[ResponseSchema])
        # print(system)
        # Define the prompt template, mapping "input" to the user_query
        prompt_template = ChatPromptTemplate.from_messages(
            [("system", system), ("human", "{input}")])

        # Chain the prompt with structured output
        few_shot_structured_llm = prompt_template | structured_llm

        # Invoke with user query properly mapped
        result = few_shot_structured_llm.invoke({"input": user_query})
        print("----------------result---------------------------------------")

        print(result)
        print("----------------result---------------------------------------")

        if 'iterable' not in result:
            self.config["config"]["prompt"] = ("You are an assistant for a question-answering tasks. "
                                               "Use the following pieces of retrieved context to answer "
                                               "the question. If you don't know the answer, say the word fallback "
                                               "Use three sentences maximum and keep the "
                                               "answer concise.")
            unstrcutured_answer = self.queryv1(user_query)
            print("in the 1st block")
            print(unstrcutured_answer['text'])
            if "fallback" in unstrcutured_answer['text']:
                return {
                    "narrative": None,
                    "is_fallback": True,
                    "faqs": [],
                    "dialogs": []
                }
            else:
                return {
                    "narrative": unstrcutured_answer['text'],
                    "is_fallback": False,
                    "faqs": [],
                    "dialogs": []
                }

        res = result['iterable']
        faq_results = []
        dialog_results = []
        narrative = None
        # extract the faqs from the result
        for item in res:
            if 'faq' in item['type']:
                faq_results.append(item)
            else:
                dialog_results.append(item)
        # check of res exists and array and have at least one element

            # case if there is one element
        print("-------------------------------------------------------")
        if len(faq_results) > 0:
            #  get back to the llm to construct the answer
            llm = self.LLM.getModel()
            cc = json.dumps(res, ensure_ascii=False)

            prompt = (
                "You are an assistant for a question-answering tasks. "
                "Use the following pieces of retrieved context to answer "
                "the question. If you don't know the answer, say that you "
                "don't know. Keep the answer concise."
                "\n\n"
                f"<context> {cc.replace('{', '{{').replace('}', '}}')} </context>"
            )
            # Define the prompt template, mapping "input" to the user_query
            prompt_template = ChatPromptTemplate.from_messages(
                [("system", prompt), ("human", "{input}")])
            # Chain the prompt with structured output
            few_shot_structured_llm = prompt_template | llm
            # Invoke with user query properly mapped
            result = few_shot_structured_llm.invoke({"input": user_query})
            print("A: ", result.content)
            narrative = result.content

        print(user_query)
        print(("-------------------------------------------------------"))

        if len(faq_results) != 0 or len(dialog_results) != 0:
            return {
                "narrative": narrative,
                "faqs": faq_results,
                "is_fallback": False,
                "dialogs": dialog_results
            }
        else:
            unstrcutured_answer = self.queryv1(user_query)
            if "fallback" in unstrcutured_answer['text']:
                return {
                    "narrative": None,
                    "is_fallback": True,
                    "faqs": [],
                    "dialogs": []
                }
            else:
                return {
                    "narrative": unstrcutured_answer['text'],
                    "is_fallback": False,
                    "faqs": [],
                    "dialogs": []
                }


#  "follow one of these cases: "
#             "1. if the user question was answerd from the history, return an empty array for metadata, return formHistory true, answer the question in the narrative."
#             "2. if the user question can be fully answerd from the context, return an array of relevant metadata items, answer the question in the narrative,   "
#             "return the metadata for the relevant items in JSON array. "
#             "relevant items mean only items that have an answer to the user question, not merely mentioning the subject of the question."
#             "Its prefeered to return a single item, but you can return multiple items if you think the user question is relevant to multiple items. "
#             # "if there was an insult in the user question, return in the array an object that have a key called 'insult' with the value of the insult. and dont return any relevant data even if there was.  "
#             "If a relevant item is found but there is a part of the user question still not answered by the found item, return the relevant items and a key isPartial with the value true"
#             "If no relevant item is found, return an empty array only and not anything else. "


    def refineHistory(self, query_history: list):
        max_tokens = 1000
        # rewrite the history in a way that it can be used by the LLM : human and ai messages

        history = []
        for query in query_history:
            history.append(HumanMessage(content=query.question))
            history.append(AIMessage(content=query.answer))

        history_trimmed = trim_messages(
            messages=history,
            max_tokens=max_tokens,
            strategy="last",
            token_counter=tiktoken_counter,
            include_system=True
        )
        return history_trimmed

    def isAr(self, text: str) -> bool:
        """
        Check if the input string contains any Arabic characters.

        :param text: The string to check.
        :return: True if the string contains Arabic characters, otherwise False.
        """
        arabic_pattern = re.compile(
            r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]')
        return bool(arabic_pattern.search(text))

    def refineV2(self, refinePayload, query_history: list):
        # print(self.config)

        original_history = query_history
        context = json.dumps(refinePayload, ensure_ascii=False)
        user_query = query_history[-1].question
        query_history = query_history[:-1]
        self.isArabic = self.isAr(user_query)

        system = (
            "You are given a user question and a context in JSON format that consists of FAQs and dialog data. Your task is to return the relevent item metadata *only* based on the provided context, and then produce output adhering to the following rules:\n"
            "1. Answer from Context\n"
            "\t- Provide an answer *strictly* based on the context.\n"
            "\t- Do not infer, guess, or assume any information that is not explicitly stated in the context.\n\n"

            "2. Use Exact Context\n"
            "\t- Refrain from making conclusions that are not grounded in the provided context.\n"
            "\t- Its totaly fine to return an empty array of no relevent items are found in the context related to the user question .\n"
            "\t- If the context does not contain enough data to fully answer the question, do not invent missing details.\n\n"

            "3. Return Relevant Metadata\n"
            "\t- In your response, include the relevant items’ metadata in a JSON array.\n\n"

            "4. IMPORTANT: Return Dialogs if Applicable\n"
            "\t- If the user is asking for a specific dialog, include the relevant dialog information (again in JSON form).\n"
            "\t- The dialogs provide a service so return them only if the user requests thier service.\n"
            "\t- If the user question was *general* about the *services you provide* do not return any dialogs and return an empty array.\n"
            "\t- Do not guess, infer, or assume that a user is asking for a dialog unless its mentiones its functionality exactly in the question\n"
            "\t- Only return the dialog if the user specifcally mentions it or its functionality in their question.\n\n"

            "5. Handle Gibberish Queries\n"
            "\t- If the user’s question is gibberish or nonsensical, return an empty JSON array only.\n\n"

            "6. Handle Partial Answers\n"
            "\t- If any part of the user’s question cannot be answered from the context (or if the context is insufficient), set isPartial to true in the output.\n\n"

            "7. Never Return Null or Undefined \n"
            "\t- Ensure all JSON fields have valid values. If something does not apply, return an empty array for the items instead of null or undefined.\n\n"

            "Finally, be sure to include the context below in your final output structure, enclosed as follows:\n\n"

            f"<context> {context.replace('{', '{{').replace('}', '}}')} </context>"
        )

        prompt_template = ChatPromptTemplate.from_messages([
            ("system", system),
            # MessagesPlaceholder("chat_history"),
            ("human", "{input}"),
        ])

        model = self.LLM.getModel()
        structured_llm = model.with_structured_output(FinalResponseSchema)

        few_shot_structured_llm = prompt_template | structured_llm

        result = few_shot_structured_llm.invoke({
            "input": user_query,
            # "chat_history": self.refineHistory(query_history)
        })
        # print("----------------result---------------------------------------")
        # print(result)

        # #  acess the item like : result["narrative"] will give the error TypeError: 'FinalResponseSchema' object is not subscriptable so use this instead : <answer>
        # print("----------------result---------------------------------------")
        # return result
        faq_results = []
        dialog_results = []
        narrative = None
        for item in result.items:
            if 'faq' in item.type:
                faq_results.append(item)
            else:
                dialog_results.append(item)

        # print("arrs: ", faq_results)
        if result.isPartial is False and len(result.items) > 0:
            # extract the faqs from the result
            if len(faq_results) == 1:
                return {
                    "faqs": faq_results,
                    "dialogs": dialog_results,
                    "narrative": faq_results[0].answer,
                    "is_fallback": False
                }

            elif len(faq_results) > 1:
                print("case 2")
                #  get back to the llm to construct the answer
                llm = self.LLM.getModel()
                cc = json.dumps(([item.dict()
                                for item in faq_results]), ensure_ascii=False)
                print("pass 1")
                # cc = json.dumps(faq_results.dict(), ensure_ascii=False)
                his = json.dumps(
                    ([item.dict() for item in query_history]), ensure_ascii=False)
                # his = json.dumps(query_history, ensure_ascii=False)
                print("pass 2")
                prompt = (
                    f"{self.config['config']['persona'] or 'You are an assistant for a question-answering tasks. '}"
                    "Use the following pieces of retrieved context and the chat history to answer "
                    "the question. If you don't know the answer, say that you "
                    "don't know. Keep the answer concise."
                    "Only answer from the provided context, chat history and dont add anything else. "
                    f"answer in {'arabic. ' if self.isArabic  else  'english. '}"
                    "\n\n"
                    f"<context> {cc.replace('{', '{{').replace('}', '}}')} </context>"
                    f"<chatHistory> {his.replace('{', '{{').replace('}', '}}')} </chatHistory>"
                )
                # Define the prompt template, mapping "input" to the user_query
                prompt_template = ChatPromptTemplate.from_messages(
                    [("system", prompt), ("human", "{input}")])
                # Chain the prompt with structured output
                few_shot_structured_llm = prompt_template | llm
                # Invoke with user query properly mapped
                result = few_shot_structured_llm.invoke({"input": user_query})
                print(("-----------------------res 2--------------------------------"))
                print("A: ", result.content)
                print(("------------------------res 2-------------------------------"))
                narrative = result.content

                return {
                    "faqs": faq_results,
                    "dialogs": dialog_results,
                    "narrative": narrative,
                    "is_fallback": False
                }

            return {
                "faqs": [],
                "dialogs": dialog_results,
                "narrative": "",
                "is_fallback": len(dialog_results) == 0,
                "unstructured": False
            }
        else:
            # print("need to folow up")
            additional_context = ""

            response = self.queryHistoryRefine(
                original_history, faq_results=faq_results, dialog_results=dialog_results)

            print("the FOLLOW ANSWER : ", response["text"])
            answer = response["text"]
            if len(answer) == 0:
                return {
                    "faqs": [],
                    "dialogs": dialog_results,
                    "narrative": "",
                    "is_fallback": len(dialog_results) == 0,
                    "unstructured": True
                }
            else:
                return {
                    "faqs": faq_results,
                    "dialogs": dialog_results,
                    "narrative": answer,
                    "is_fallback": False,
                    "unstructured": True
                }

        return {
            "is_fallback": True,
            "narrative": '',
            "faqs": [],
            "dialogs": []
        }
        
    def safe_prompt_processor(self,raw_template: str):
        """
        Processes templates containing both literal JSON and template variables.
        Automatically escapes curly braces in JSON while preserving template variables.
        """
        # Find and protect template variables
        variables = re.findall(r'{\w+}', raw_template)
        protected = []
        
        # Replace variables with temporary markers
        for idx, var in enumerate(variables):
            marker = f"__MARKER_{idx}__"
            protected.append((marker, var))
            raw_template = raw_template.replace(var, marker, 1)

        # Escape remaining curly braces
        processed_template = (
            raw_template
            .replace("{", "{{")
            .replace("}", "}}")
        )

        # Restore original variables
        for marker, var in protected:
            processed_template = processed_template.replace(marker, var)

        # Extract final variables from template
        final_vars = re.findall(r'{\w+}', processed_template)
        clean_vars = [v[1:-1] for v in final_vars]  # Remove surrounding braces

        return PromptTemplate(
            template=processed_template,
            input_variables=clean_vars
        )
        
    def summarizationAgent(self, payload):
        schema_text = (
            "Summarize the following data in a concise way:\n\n"
            "Return a short summary of the data in a narrative way aimed at a user to read."
        )

        temp = f"{schema_text}\n\nInput data: {{input_data}}"

        prompt = PromptTemplate.from_template(temp)

        llm = self.LLM.getModel()
        chain = prompt | llm | StrOutputParser()

        try:
            response = chain.invoke({'input_data': payload.input_data})
            return {"response": response}
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to parse JSON response: {str(e)}")

    def textractionAgent(self, payload):
        # schema_instructions = [
        #     f"- {key} ({config.get('type', 'string')}): {config.get('description', '')}"
        #     for key, config in payload.output_structure.items()
        # ]
        schema_instructions = [
            f"- {field.var_name} ({field.type}): {field.description}"
            for field in payload.output_structure
        ]


        schema_text = (
            "Your job is to extract all relevant information for items from the following context and return them."
            "Your response MUST be a valid JSON object with these properties and MUST respect the type:\n"
            + "\n".join(schema_instructions)
            + "\nEnsure proper JSON formatting with double quotes and no trailing commas."
            + "\nIf a property is not present, respond with null."
        )

        temp = f"{schema_text}\n\nInput data: {{input_data}}"

        prompt = PromptTemplate.from_template(temp)

        llm = self.LLM.getModel()
        chain = prompt | llm | JsonOutputParser()

        try:
            response = chain.invoke({'input_data': payload.input_data})
            return {"response": response}
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to parse JSON response: {str(e)}")
            
    def sentimentAgent(self, payload):
        schema_text = (
            "Analyze the sentiment of the following text and provide a score between -1 and 1."
            "Your response MUST be a valid JSON object with these properties and MUST respect the type:\n"
            "\n"
            "- sentiment (string): The sentiment of the text, either 'positive', 'negative', or 'neutral'."
            "\n"
            "- score (float): A number between -1 and 1 representing the sentiment score."
        )

        temp = f"{schema_text}\n\nInput data: {{input_data}}"

        prompt = PromptTemplate.from_template(temp)

        llm = self.LLM.getModel()
        chain = prompt | llm | JsonOutputParser()

        try:
            response = chain.invoke({'input_data': payload.input_data})
            return {"response": response}
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to parse JSON response: {str(e)}")
            
    def customAgent(self, payload):
        if payload.output_structure is not None:
            print('here')
            schema_instructions = [
                f"- {field.var_name} ({field.type}): {field.description}"
                for field in payload.output_structure
            ]

            schema_text = (
                "Your response MUST be a valid JSON object with these properties:\n"
                + "\n".join(schema_instructions)
                + "\nEnsure proper JSON formatting with double quotes and no trailing commas."
                + "\nIf a property is not present, respond with null."
            )

            temp = f"{payload.custom_prompt}\n\n{schema_text}\n\nInput data: {{input_data}}"

            # prompt = PromptTemplate.from_template(temp)
            prompt = self.safe_prompt_processor(temp)

            llm = self.LLM.getModel()
            chain = prompt | llm | JsonOutputParser()

            try:
                response = chain.invoke({'input_data': payload.input_data})
                return {"response": response}
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"Failed to parse JSON response: {str(e)}")
        else:
            temp = f"{payload.custom_prompt}\n\n" + "{input_data}"
            print(temp)
            # prompt = PromptTemplate.from_template(temp)
            prompt = self.safe_prompt_processor(temp)
            # print(prompt22)
            
            print('else')
            print(prompt)
            llm = self.LLM.getModel()
            chain = prompt | llm | StrOutputParser()
            try:
                response = chain.invoke({'input_data': payload.input_data})
                return {"response": response}
            except Exception as e:
                print(e)
                raise HTTPException(
                    status_code=500, detail=f"Failed to parse JSON response: {str(e)}")
            response = chain.invoke({
                'input_data': payload.input_data
            })
            print(response)
            return {"response": response}

    def agentBlock(self, payload: PayloadAgent):
        try:
            if payload.task_type == 'textraction' and payload.output_structure:
                return self.textractionAgent(payload)
            elif payload.task_type == 'sentiment':
                return self.sentimentAgent(payload)
            elif payload.task_type == 'summarization':
                return self.summarizationAgent(payload)
            elif payload.task_type == 'custom' and payload.custom_prompt:
                return self.customAgent(payload)
            else:
                raise HTTPException(
                    status_code=400, detail="Invalid task type or missing required fields")

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
