

To run the project first setup venv:

```bash
# On Windows:
python -m venv venv
venv\Scripts\activate

# On macOS/Linux:
python3 -m venv venv
source venv/bin/activate
```

Then install deps boii:

```bash
pip install -r reqs.txt
```

```bash
uvicorn --reload main:app
```




  response_schema = {
            "title": "response_items",
            "description": "The list of metadata for the relevant item",
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "source_id": {
                        "type": "string",
                        "description": "source_id in the metadata"
                    },
                    "type": {
                        "type": "string",
                        "description": "type in the metadata, one of (qna, dialog)"
                    },
                    "answer": {
                        "type": "string",
                        "description": "answer in the metadata"
                    }
                },
                "required": ["source_id", "type", "answer"]  # Adding required fields may help with structure validation
            }
        }
pip install uvicorn
python -m pip install --upgrade pip
pip install langchain_openai langchain_unstructured langchain_text_splitters langchain_community langchain_chroma fastapi pydantic langchain langchain_core requests chromadb 
pip install beautifulsoup4
pip install unstructured
pip install python-magic-bin python-magic-bin "unstructured[xlsx]"
pip install langchain_cohere
pip install langchain_milvus


uvicorn main:app --host 0.0.0.0 --port 8585 --reload



### semantic

Percentile
The default way to split is based on percentile. In this method, all differences between sentences are calculated, and then any difference greater than the X percentile is split.

Standard Deviation
In this method, any difference greater than X standard deviations is split.

Interquartile
In this method, the interquartile distance is used to split chunks.

Gradient
In this method, the gradient of distance is used to split chunks along with the percentile method. This method is useful when chunks are highly correlated with each other or specific to a domain e.g. legal or medical. The idea is to apply anomaly detection on gradient array so that the distribution become wider and easy to identify boundaries in highly semantic data.

```json
{
  "chunking_strategy": "semantic",
  "breakpoint_threshold_type": [
    "percentile", // default
    "standard_deviation",
    "interquartile",
    "gradient"
  ]
}
```

### Character-based Splitting

Splits text into chunks based on a specified number of characters.
Useful for consistent chunk sizes regardless of content structure.

```json
{
  "chunking_strategy": "CharacterTextSplitter",
  "chunk_size": 1000,
  "chunk_overlap": 200,
  "separator": "\n\n"
}
```

### RecursiveCharacterTextSplitter

refrences:
https://python.langchain.com/docs/how_to/recursive_text_splitter/

```json
{
  "chunking_strategy": "RecursiveCharacterTextSplitter",
  "chunk_size": 1000,
  "chunk_overlap": 200,
  "separators": null, // e.g: ["\n", "----"]
  "keep_separator": true // || "start" || "end"
}
```

### SentenceTransformersTokenTextSplitter

```json
{
  "chunking_strategy": "SentenceTransformersTokenTextSplitter",
  "chunk_size": 1000, // ? idk how much
  "chunk_overlap": 50, // def in the func docs 50 so its [optional]
  "model_name": "sentence-transformers/all-mpnet-base-v2", // select between the def or, hi its me again couldn't find refs for the model names that we can use :(
  "tokens_per_chunk": null // number + also idk how much
}
```

### TokenTextSplitter

```json
{
  "chunking_strategy": "TokenTextSplitter",
  "chunk_size": 1000,
  "chunk_overlap": 50
}
```
