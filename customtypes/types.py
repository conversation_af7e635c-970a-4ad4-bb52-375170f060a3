from dataclasses import dataclass
from typing import Optional, List, Dict, Literal
from pydantic import BaseModel, Field


@dataclass
class LlmConfig:
    llm_key: str
    llm_type: str
    llm_model: str
    llm_temperature: float


@dataclass
class StoreConfig:
    store_url: str
    store_type: str
    collection_name: str


@dataclass
class PayloadConfig:
    chunk_size: Optional[int] = 2500
    chunk_overlap: Optional[int] = 250
    top_k: Optional[int] = 20
    fetch_k: Optional[int] = 50
    lambda_mult: Optional[float] = 0.5
    prompt: Optional[str] = None
    persona: Optional[str] = None
    chunking_strategy: Optional[str] = None
    breakpoint_threshold_type: Optional[str] = "percentile"
    separator: Optional[str] = "\n\n"
    separators: Optional[List[str]] = None
    keep_separator: Optional[bool | str] = True
    tokens_per_chunk: Optional[int] = None
    model_name: Optional[str] = "sentence-transformers/all-mpnet-base-v2"


@dataclass
class Payload:
    llm_config: LlmConfig
    store_config: StoreConfig
    config: PayloadConfig


@dataclass
class DocumentI:
    source_type: str  # [pdf, txt, docx]
    document_url: str


@dataclass
class WebpageI:
    webpage_url: str


@dataclass
class SourceI:
    # document: [pdf, text, docx], webpage, googleDrive, email
    source_type: str
    document: Optional[DocumentI] = None
    webpage: Optional[WebpageI] = None


@dataclass
class MetadataRefine:
    source_id: str
    type: str
    answer: str


@dataclass
class PayloadRefine:
    pageContent: str
    metadata: MetadataRefine


@dataclass
class OutputStructure:
    var_name: str
    type: str
    description: str

@dataclass
class PayloadAgent:
    task_type: Literal["textraction", "summarization", "sentiment", "custom"] = Field(
        ..., description="The type of task the agent should perform (e.g., 'entity_extraction', 'summarization', 'custom')")
    input_data: Dict = Field(
        default={}, description="The data that the LLM should process (e.g., API response, user input)")
    custom_prompt: Optional[str] = Field(
        None, description="Custom prompt to override predefined logic")
    # output_structure: Optional[Dict[str, Dict[str, str]]] = Field(
    #     None,
    #     description="Expected structure of the output in {key: {description: str, type: str}} format."
    # )
    output_structure: Optional[List[OutputStructure]] = Field(
        None,
        description="Expected structure of the output in [{var_name: str, type: str, description: str}] format."
    )
